import type { StorybookConfig } from '@storybook/react-vite';

const config: StorybookConfig = {
  stories: ['../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
  addons: ['@storybook/addon-docs', '@storybook/addon-vitest'],
  framework: {
    name: '@storybook/react-vite',
    options: {},
  },
  viteFinal: async (config) => {
    config.optimizeDeps = {
      include: [
        ...(config.optimizeDeps?.include || []),
        'react/jsx-dev-runtime',
      ],
    };
    return config;
  },
};
export default config;
