import { fireEvent, render, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { Rarity } from '../models/Rarity';
import { rarityToChar, SetSymbol, SetSymbolSize } from './SetSymbol';

// Mock the dependencies
vi.mock('../helpers/core_assets', () => ({
  CoreAssets: {
    iconHost: vi.fn(() => 'https://test-icons.example.com'),
  },
}));

type SetSymbolProps = React.ComponentProps<typeof SetSymbol>;

const mockSetName = 'Test set';

const DEFAULT_PROPS: SetSymbolProps = {
  setName: mockSetName,
  setCode: 'TST',
  hoverText: false,
};

function renderSetSymbol(props: Partial<SetSymbolProps> = {}) {
  return render(<SetSymbol {...DEFAULT_PROPS} {...props} />);
}

describe('rarityToChar', () => {
  it('returns undefined for undefined rarity', () => {
    expect(rarityToChar(undefined)).toBeUndefined();
  });

  it('returns "C" for BASIC_LAND', () => {
    expect(rarityToChar(Rarity.BASIC_LAND)).toBe('C');
  });

  it('returns "C" for COMMON', () => {
    expect(rarityToChar(Rarity.COMMON)).toBe('C');
  });

  it('returns "U" for UNCOMMON', () => {
    expect(rarityToChar(Rarity.UNCOMMON)).toBe('U');
  });

  it('returns "R" for RARE', () => {
    expect(rarityToChar(Rarity.RARE)).toBe('R');
  });

  it('returns "M" for MYTHIC_RARE', () => {
    expect(rarityToChar(Rarity.MYTHIC_RARE)).toBe('M');
  });

  it('returns "S" for SPECIAL', () => {
    expect(rarityToChar(Rarity.SPECIAL)).toBe('S');
  });

  it('logs error and returns undefined for invalid rarity', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    const invalidRarity = 'invalid' as Rarity;

    const result = rarityToChar(invalidRarity);

    expect(result).toBeUndefined();
    expect(consoleSpy).toHaveBeenCalledWith(
      'Invalid rarity invalid converted to COMMON',
    );

    consoleSpy.mockRestore();
  });
});

describe('SetSymbol', () => {
  describe('component render', () => {
    it('renders with basic props', () => {
      renderSetSymbol();

      expect(screen.getByRole('img')).toBeInTheDocument();
    });

    it('defaults to XS size when no size is provided', () => {
      renderSetSymbol();

      expect(screen.getByRole('img').parentElement?.classList).toContain(
        'set-symbol__xs',
      );
    });

    it('applies correct size class when size is provided', () => {
      renderSetSymbol({ size: SetSymbolSize.XL });

      expect(screen.getByRole('img').parentElement?.classList).toContain(
        'set-symbol__xl',
      );
    });

    it('generates correct image source without rarity', () => {
      renderSetSymbol({ setCode: 'ABC' });
      const img = screen.getByRole('img') as HTMLImageElement;

      expect(img.src).toBe(
        'https://test-icons.example.com/set_symbols/ABC_large.png',
      );
    });

    it('generates correct image source with rarity', () => {
      renderSetSymbol({
        setCode: 'ABC',
        rarity: Rarity.RARE,
      });
      const img = screen.getByRole('img') as HTMLImageElement;

      expect(img.src).toBe(
        'https://test-icons.example.com/set_symbols/ABC_R_large.png',
      );
    });
  });

  describe('hover functionality', () => {
    /**
     * Hover test using class name when it should be finding an element with role tooltip
     * This indicates that we should migrate this properly to a `role="tooltip"` element.
     */
    it('does not show tooltip when hoverText is false', () => {
      const { container } = renderSetSymbol({ hoverText: false });
      const tooltipContainer = container.querySelector(
        '.set-symbol-tooltip-container',
      );

      expect(tooltipContainer).not.toBeInTheDocument();
    });

    it('does not show tooltip initially when hoverText is true', () => {
      const { container } = renderSetSymbol({ hoverText: true });
      const tooltipContainer = container.querySelector(
        '.set-symbol-tooltip-container',
      );

      expect(tooltipContainer).not.toBeInTheDocument();
    });

    it('shows tooltip on mouse enter when hoverText is true', () => {
      const mockRarity = Rarity.RARE;
      const mockCollectorNumber = '123';

      renderSetSymbol({
        hoverText: true,
        rarity: mockRarity,
        collectorNumber: mockCollectorNumber,
      });

      const symbolDiv = screen.getByRole('img').parentElement as HTMLDivElement;
      fireEvent.mouseEnter(symbolDiv);

      expect(screen.getByText(mockSetName)).toBeInTheDocument();
      expect(
        screen.getByText(`${mockRarity} - ${mockCollectorNumber}`, {
          exact: false,
        }),
      ).toBeInTheDocument();
    });

    it('hides tooltip on mouse leave', () => {
      renderSetSymbol({ hoverText: true });
      const symbolDiv = screen.getByRole('img').parentElement as HTMLDivElement;

      // Show tooltip first
      fireEvent.mouseEnter(symbolDiv);
      expect(screen.getByText(mockSetName)).toBeInTheDocument();

      // Hide tooltip
      fireEvent.mouseLeave(symbolDiv);
      expect(screen.queryByText(mockSetName)).not.toBeInTheDocument();
    });

    it('shows tooltip with rarity but no collector number', () => {
      renderSetSymbol({
        hoverText: true,
        rarity: Rarity.UNCOMMON,
      });

      const symbolDiv = screen.getByRole('img').parentElement as HTMLDivElement;
      fireEvent.mouseEnter(symbolDiv);

      expect(screen.getByText(mockSetName)).toBeInTheDocument();
      expect(
        screen.getByText(Rarity.UNCOMMON, { exact: false }),
      ).toBeInTheDocument();
    });

    it.only('shows tooltip with collector number but no rarity', async () => {
      const mockCollectorNumber = '456';

      renderSetSymbol({
        hoverText: true,
        collectorNumber: mockCollectorNumber,
      });

      const symbolDiv = screen.getByRole('img').parentElement as HTMLDivElement;
      fireEvent.mouseEnter(symbolDiv);
      console.log(mockSetName);
      expect(await screen.findByText(mockSetName)).toBeInTheDocument();
      expect(screen.getByText(`- ${mockCollectorNumber}`)).toBeInTheDocument();
    });
  });
});
