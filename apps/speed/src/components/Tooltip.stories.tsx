import type { Meta, StoryObj } from '@storybook/react-vite';
import { expect, screen, waitFor } from 'storybook/test';
import { Tooltip, TooltipContent, TooltipTrigger } from './Tooltip';

const meta: Meta<typeof Tooltip> = {
  title: 'Components/Tooltip',
  component: Tooltip,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A reusable tooltip component built with floating-ui that provides accessible, well-positioned tooltips.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    placement: {
      control: 'select',
      options: ['top', 'bottom', 'left', 'right'],
      description: 'Preferred placement of the tooltip',
    },
    initialOpen: {
      control: 'boolean',
      description: 'Initial open state of the tooltip',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    placement: 'top',
  },
  render: (args) => (
    <div className="p-8">
      <Tooltip {...args}>
        <TooltipTrigger>Hover me</TooltipTrigger>
        <TooltipContent>This is a basic tooltip!</TooltipContent>
      </Tooltip>
    </div>
  ),
  play: async ({ canvas, userEvent }) => {
    const trigger = canvas.getByRole('button');
    await userEvent.hover(trigger);
    await waitFor(() => {
      expect(screen.getByRole('tooltip')).toBeInTheDocument();
    });
  },
};

export const Placements: Story = {
  render: () => (
    <div className="p-16 grid grid-cols-2 gap-8 place-items-center">
      <Tooltip placement="top">
        <TooltipTrigger>Top</TooltipTrigger>
        <TooltipContent>Tooltip on top</TooltipContent>
      </Tooltip>

      <Tooltip placement="bottom">
        <TooltipTrigger>Bottom</TooltipTrigger>
        <TooltipContent>Tooltip on bottom</TooltipContent>
      </Tooltip>

      <Tooltip placement="left">
        <TooltipTrigger>Left</TooltipTrigger>
        <TooltipContent>Tooltip on left</TooltipContent>
      </Tooltip>

      <Tooltip placement="right">
        <TooltipTrigger>Right</TooltipTrigger>
        <TooltipContent>Tooltip on right</TooltipContent>
      </Tooltip>
    </div>
  ),
};

export const CustomTrigger: Story = {
  render: () => (
    <div className="p-8">
      <Tooltip>
        <TooltipTrigger asChild>
          <span className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-md cursor-pointer hover:bg-blue-600 transition-colors">
            Custom Element
          </span>
        </TooltipTrigger>
        <TooltipContent>
          This tooltip is attached to a custom element!
        </TooltipContent>
      </Tooltip>
    </div>
  ),
};

export const RichContent: Story = {
  render: () => (
    <div className="p-8">
      <Tooltip>
        <TooltipTrigger>Card Information</TooltipTrigger>
        <TooltipContent>
          <div className="space-y-1">
            <div className="font-semibold">Lightning Bolt</div>
            <div className="text-xs space-y-0.5">
              <div>Cost: R</div>
              <div>Type: Instant</div>
              <div>Power/Toughness: -/-</div>
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </div>
  ),
};

export const LongContent: Story = {
  render: () => (
    <div className="p-8">
      <Tooltip>
        <TooltipTrigger>Long Content</TooltipTrigger>
        <TooltipContent>
          <div className="max-w-xs">
            This is a tooltip with much longer content that demonstrates how the
            tooltip handles text wrapping and maintains readability even with
            extensive information.
          </div>
        </TooltipContent>
      </Tooltip>
    </div>
  ),
};

export const MultipleTooltips: Story = {
  render: () => (
    <div className="p-8 flex gap-4 flex-wrap">
      <Tooltip>
        <TooltipTrigger>First</TooltipTrigger>
        <TooltipContent>First tooltip</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>Second</TooltipTrigger>
        <TooltipContent>Second tooltip</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger>Third</TooltipTrigger>
        <TooltipContent>Third tooltip</TooltipContent>
      </Tooltip>

      <Tooltip placement="bottom">
        <TooltipTrigger>Fourth (Bottom)</TooltipTrigger>
        <TooltipContent>Fourth tooltip positioned at bottom</TooltipContent>
      </Tooltip>
    </div>
  ),
};

export const WithIcons: Story = {
  render: () => (
    <div className="p-8 flex gap-4 items-center">
      <Tooltip>
        <TooltipTrigger asChild>
          <button className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700">
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </button>
        </TooltipTrigger>
        <TooltipContent>Information</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <button className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700">
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
          </button>
        </TooltipTrigger>
        <TooltipContent>Settings</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <button className="p-2 rounded-full bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 text-red-600 dark:text-red-400">
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
              />
            </svg>
          </button>
        </TooltipTrigger>
        <TooltipContent>Delete</TooltipContent>
      </Tooltip>
    </div>
  ),
};
