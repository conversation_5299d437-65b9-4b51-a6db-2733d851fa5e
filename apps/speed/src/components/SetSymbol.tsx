import { Rarity } from '~/models/Rarity';
import { CoreAssets } from '../helpers/core_assets';
import { capitalize } from '../helpers/fmt';
import { Tooltip, TooltipContent, TooltipTrigger } from './Tooltip';

export enum SetSymbolSize {
  XS = 'xs',
  SM = 'sm',
  MD = 'md',
  XL = 'xl',
}

export interface SetSymbolProps {
  setName: string;
  setCode: string;
  hoverText: boolean;
  collectorNumber?: string;
  rarity?: Rarity;
  size?: SetSymbolSize;
}

export function rarityToChar(rarity?: Rarity) {
  switch (rarity) {
    case Rarity.BASIC_LAND:
    case Rarity.COMMON:
      return 'C';
    case Rarity.UNCOMMON:
      return 'U';
    case Rarity.RARE:
      return 'R';
    case Rarity.MYTHIC_RARE:
      return 'M';
    case Rarity.SPECIAL:
      return 'S';
    default:
      // TODO: Add Bugsnag notifier when we work out how to get that working without causing <PERSON><PERSON> to lose it.
      console.error(`Invalid rarity ${rarity} converted to COMMON`);
      return undefined;
  }
}

export function SetSymbol({
  collectorNumber,
  hoverText,
  size = SetSymbolSize.XS,
  setName,
  setCode,
  rarity,
}: SetSymbolProps) {
  const char = rarityToChar(rarity);
  const rarityHoverMessage = capitalize(rarity);
  const collectorNumberText = collectorNumber ? ' - ' + collectorNumber : '';
  let imgSrc = `${CoreAssets.iconHost()}/set_symbols/${setCode}_large.png`;

  if (char) {
    imgSrc = `${CoreAssets.iconHost()}/set_symbols/${setCode}_${char}_large.png`;
  }

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div className={`flex set-symbol__${size}`}>
          <img src={imgSrc} alt={`${setName} set symbol`} />
        </div>
      </TooltipTrigger>
      {hoverText && (
        <TooltipContent>
          <div className="set-symbol-tooltip-container">
            <div className="set-symbol-tooltip">
              <div>{setName}</div>
              <div>{`${rarityHoverMessage}${collectorNumberText}`}</div>
            </div>
          </div>
        </TooltipContent>
      )}
    </Tooltip>
  );
}
