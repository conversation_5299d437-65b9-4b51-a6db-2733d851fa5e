import type { Meta, StoryObj } from '@storybook/react-vite';
import { expect, screen, waitFor } from 'storybook/test';
import { Rarity } from '~/models/Rarity';
import { SetSymbol, SetSymbolSize } from './SetSymbol';

const meta: Meta<typeof SetSymbol> = {
  title: 'Components/SetSymbol',
  component: SetSymbol,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A set symbol component that displays Magic: The Gathering set symbols with rarity indicators and tooltips.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    setName: {
      control: 'text',
      description: 'The full name of the set',
    },
    setCode: {
      control: 'text',
      description: 'The three-letter set code',
    },
    collectorNumber: {
      control: 'text',
      description: 'Optional collector number for the card',
    },
    rarity: {
      control: 'select',
      options: Object.values(Rarity),
      description: 'The rarity of the card',
    },
    size: {
      control: 'select',
      options: Object.values(SetSymbolSize),
      description: 'Size of the set symbol',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    setName: 'Dominaria United',
    setCode: 'dmu',
    hoverText: true,
    rarity: Rarity.RARE,
    size: SetSymbolSize.MD,
  },
  play: async ({ canvas, userEvent }) => {
    const setSymbol = canvas.getByRole('img').parentElement as HTMLDivElement;
    await userEvent.hover(setSymbol);
    await waitFor(() => {
      expect(screen.getByRole('tooltip')).toBeInTheDocument();
      expect(screen.getByText('Dominaria United')).toBeInTheDocument();
    });
  },
};

export const AllSizes: Story = {
  render: () => (
    <div className="p-8 flex gap-4 items-center">
      <div className="text-center">
        <SetSymbol
          setName="Dominaria United"
          setCode="dmu"
          hoverText={true}
          rarity={Rarity.RARE}
          size={SetSymbolSize.XS}
        />
        <div className="text-xs mt-2">XS</div>
      </div>
      <div className="text-center">
        <SetSymbol
          setName="Dominaria United"
          setCode="dmu"
          hoverText={true}
          rarity={Rarity.RARE}
          size={SetSymbolSize.SM}
        />
        <div className="text-xs mt-2">SM</div>
      </div>
      <div className="text-center">
        <SetSymbol
          setName="Dominaria United"
          setCode="dmu"
          hoverText={true}
          rarity={Rarity.RARE}
          size={SetSymbolSize.MD}
        />
        <div className="text-xs mt-2">MD</div>
      </div>
      <div className="text-center">
        <SetSymbol
          setName="Dominaria United"
          setCode="dmu"
          hoverText={true}
          rarity={Rarity.RARE}
          size={SetSymbolSize.XL}
        />
        <div className="text-xs mt-2">XL</div>
      </div>
    </div>
  ),
};

export const AllRarities: Story = {
  render: () => (
    <div className="p-8 grid grid-cols-3 gap-4 place-items-center">
      <div className="text-center">
        <SetSymbol
          setName="Dominaria United"
          setCode="dmu"
          hoverText={true}
          rarity={Rarity.COMMON}
          size={SetSymbolSize.MD}
        />
        <div className="text-xs mt-2">Common</div>
      </div>
      <div className="text-center">
        <SetSymbol
          setName="Dominaria United"
          setCode="dmu"
          hoverText={true}
          rarity={Rarity.UNCOMMON}
          size={SetSymbolSize.MD}
        />
        <div className="text-xs mt-2">Uncommon</div>
      </div>
      <div className="text-center">
        <SetSymbol
          setName="Dominaria United"
          setCode="dmu"
          hoverText={true}
          rarity={Rarity.RARE}
          size={SetSymbolSize.MD}
        />
        <div className="text-xs mt-2">Rare</div>
      </div>
      <div className="text-center">
        <SetSymbol
          setName="Dominaria United"
          setCode="dmu"
          hoverText={true}
          rarity={Rarity.MYTHIC_RARE}
          size={SetSymbolSize.MD}
        />
        <div className="text-xs mt-2">Mythic Rare</div>
      </div>
      <div className="text-center">
        <SetSymbol
          setName="Dominaria United"
          setCode="dmu"
          hoverText={true}
          rarity={Rarity.BASIC_LAND}
          size={SetSymbolSize.MD}
        />
        <div className="text-xs mt-2">Basic Land</div>
      </div>
      <div className="text-center">
        <SetSymbol
          setName="Dominaria United"
          setCode="dmu"
          hoverText={true}
          rarity={Rarity.SPECIAL}
          size={SetSymbolSize.MD}
        />
        <div className="text-xs mt-2">Special</div>
      </div>
    </div>
  ),
};

export const WithCollectorNumber: Story = {
  args: {
    setName: 'Dominaria United',
    setCode: 'dmu',
    hoverText: true,
    rarity: Rarity.MYTHIC_RARE,
    collectorNumber: '123',
    size: SetSymbolSize.MD,
  },
  play: async ({ canvas, userEvent }) => {
    const setSymbol = canvas.getByRole('img').parentElement as HTMLDivElement;
    await userEvent.hover(setSymbol);
    await waitFor(() => {
      expect(screen.getByRole('tooltip')).toBeInTheDocument();
      expect(
        screen.getByText('Mythic rare - 123', { exact: false }),
      ).toBeInTheDocument();
    });
  },
};

export const NoRarity: Story = {
  args: {
    setName: 'Dominaria United',
    setCode: 'dmu',
    hoverText: true,
    size: SetSymbolSize.MD,
  },
  render: (args) => (
    <div className="p-8">
      <div className="text-center">
        <SetSymbol {...args} />
        <div className="text-xs mt-2">No rarity specified</div>
      </div>
    </div>
  ),
};

export const DifferentSets: Story = {
  render: () => (
    <div className="p-8 grid grid-cols-2 gap-6 place-items-center">
      <div className="text-center">
        <SetSymbol
          setName="The Brothers' War"
          setCode="bro"
          hoverText={true}
          rarity={Rarity.RARE}
          size={SetSymbolSize.MD}
        />
        <div className="text-xs mt-2">The Brothers&apos; War</div>
      </div>
      <div className="text-center">
        <SetSymbol
          setName="Phyrexia: All Will Be One"
          setCode="one"
          hoverText={true}
          rarity={Rarity.MYTHIC_RARE}
          size={SetSymbolSize.MD}
        />
        <div className="text-xs mt-2">Phyrexia: All Will Be One</div>
      </div>
      <div className="text-center">
        <SetSymbol
          setName="March of the Machine"
          setCode="mom"
          hoverText={true}
          rarity={Rarity.UNCOMMON}
          size={SetSymbolSize.MD}
        />
        <div className="text-xs mt-2">March of the Machine</div>
      </div>
      <div className="text-center">
        <SetSymbol
          setName="Wilds of Eldraine"
          setCode="woe"
          hoverText={true}
          rarity={Rarity.COMMON}
          size={SetSymbolSize.MD}
        />
        <div className="text-xs mt-2">Wilds of Eldraine</div>
      </div>
    </div>
  ),
};

export const MultipleSymbols: Story = {
  render: () => (
    <div className="p-8 flex gap-2 items-center flex-wrap">
      <SetSymbol
        setName="Dominaria United"
        setCode="dmu"
        hoverText={true}
        rarity={Rarity.COMMON}
        size={SetSymbolSize.SM}
      />
      <SetSymbol
        setName="Dominaria United"
        setCode="dmu"
        hoverText={true}
        rarity={Rarity.UNCOMMON}
        size={SetSymbolSize.SM}
      />
      <SetSymbol
        setName="Dominaria United"
        setCode="dmu"
        hoverText={true}
        rarity={Rarity.RARE}
        size={SetSymbolSize.SM}
      />
      <SetSymbol
        setName="Dominaria United"
        setCode="dmu"
        hoverText={true}
        rarity={Rarity.MYTHIC_RARE}
        size={SetSymbolSize.SM}
      />
    </div>
  ),
};
