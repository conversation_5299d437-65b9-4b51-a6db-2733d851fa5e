import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

const Welcome = () => {
  return (
    <div className="max-w-4xl mx-auto p-8">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          CardCastle Speed App
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-400">
          Component Library & Design System
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-8 mb-12">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
          <h2 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
            🎨 Design System
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Built with Tailwind CSS and designed for both light and dark modes.
          </p>
          <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
            <li>• Responsive design patterns</li>
            <li>• Consistent spacing and typography</li>
            <li>• Accessible color schemes</li>
            <li>• Dark mode support</li>
          </ul>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
          <h2 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
            🧩 Components
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Reusable React components built with TypeScript and modern patterns.
          </p>
          <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
            <li>• Fully typed with TypeScript</li>
            <li>• Accessible by default</li>
            <li>• Comprehensive testing</li>
            <li>• Flexible and composable</li>
          </ul>
        </div>
      </div>

      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950 rounded-lg p-8 mb-8">
        <h2 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
          🚀 Getting Started
        </h2>
        <div className="grid md:grid-cols-3 gap-6">
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
              1. Browse Components
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Explore the component library in the sidebar to see all available components.
            </p>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
              2. View Examples
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Each component includes multiple examples showing different use cases.
            </p>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
              3. Copy Code
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Use the "Show code" feature to copy component usage examples.
            </p>
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
        <h2 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
          📚 Available Components
        </h2>
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
              UI Components
            </h3>
            <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
              <li>• Tooltip - Accessible tooltips with floating-ui</li>
              <li>• DefaultCatchBoundary - Error boundary component</li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
              Coming Soon
            </h3>
            <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
              <li>• Button variants</li>
              <li>• Form components</li>
              <li>• Navigation components</li>
              <li>• Card layouts</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="mt-12 text-center">
        <p className="text-gray-500 dark:text-gray-500 text-sm">
          Built with ❤️ for the CardCastle team
        </p>
      </div>
    </div>
  );
};

const meta: Meta<typeof Welcome> = {
  title: 'Welcome',
  component: Welcome,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'Welcome to the CardCastle Speed App component library and design system.',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {};
