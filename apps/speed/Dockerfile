ARG VITALIZE_PACKAGE_TOKEN
ARG CI_PROJECT_ID

# Build stage
FROM node:22-slim AS base

# Set working directory
WORKDIR /app

# Install pnpm
RUN corepack enable && corepack prepare pnpm@latest --activate

# Copy root workspace files
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY eslint.config.mjs .prettierrc.mjs ./
COPY vitest.shared.ts ./

# Copy app source
COPY apps/speed ./apps/speed

# Generate a temporary npmrc that is configured to read from GitLab's NPM registry

# Add this to your CI script temporarily to debug
RUN echo "@cardcastle:registry=https://gitlab.com/api/v4/projects/${CI_PROJECT_ID}/packages/npm/" >> .npmrc
RUN echo "//gitlab.com/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${VITALIZE_PACKAGE_TOKEN}" >> .npmrc
RUN cat .npmrc

# Install dependencies
RUN pnpm install --frozen-lockfile --ignore-scripts
RUN pnpm --filter speed exec playwright install

# Build stage
FROM base AS build
# Set working directory
WORKDIR /app
# Build the app
RUN pnpm --filter speed run build

# Production stage
FROM node:22-slim AS production

# Set working directory
WORKDIR /app

# Copy build artifacts
COPY --from=build /app/apps/speed/.output ./apps/speed/.output

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000

# Expose the port the app runs on
EXPOSE 3000

# Command to run the application
WORKDIR /app/apps/speed
CMD ["node", ".output/server/index.mjs"]