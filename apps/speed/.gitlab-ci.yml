# This file extends the root CI configuration with app-specific settings

stages:
  - build
  - test
  - deploy

before_script:
  - export BUILD_TAG="${CI_COMMIT_REF_NAME}_${CI_COMMIT_SHA}"
  - export BUILD_TAG=`echo $BUILD_TAG | sed -e 's/\//-/g'`
  - export SANITIZED_REF=`echo $CI_COMMIT_REF_NAME | sed -e 's/\//-/g'`

.speed-common:
  rules:
    - changes:
      - "**/apps/speed/*"
      when: always

# Build temporary Docker image for tests
speed-build-test:
  stage: build
  image: docker:stable
  services:
    - docker:dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_DRIVER: overlay2
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_JOB_TOKEN $CI_REGISTRY
    - docker pull $CI_REGISTRY_SPEED_IMAGE/test:$SANITIZED_REF || true
    # Build a test image cached from the recently build production image
    - |
      docker build . -f ./apps/speed/Dockerfile --target base \
        --build-arg VITALIZE_PACKAGE_TOKEN=$VITALIZE_PACKAGE_TOKEN \
        --build-arg CI_PROJECT_ID=$CI_PROJECT_ID \
        --cache-from $CI_REGISTRY_SPEED_IMAGE/test:$SANITIZED_REF \
        -t $CI_REGISTRY_SPEED_IMAGE/test:$CI_PIPELINE_ID \
        -t $CI_REGISTRY_SPEED_IMAGE/test:$SANITIZED_REF
    - docker push $CI_REGISTRY_SPEED_IMAGE/test:$CI_PIPELINE_ID
    - docker push $CI_REGISTRY_SPEED_IMAGE/test:$SANITIZED_REF
  extends: .speed-common

speed-build:
  stage: build
  image: docker:stable
  services:
    - docker:dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_DRIVER: overlay2
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_JOB_TOKEN $CI_REGISTRY
    - docker pull $CI_REGISTRY_SPEED_IMAGE:$SANITIZED_REF || true
    - |
      docker build . -f ./apps/speed/Dockerfile --target base \
        --build-arg VITALIZE_PACKAGE_TOKEN=$VITALIZE_PACKAGE_TOKEN \
        --build-arg CI_PROJECT_ID=$CI_PROJECT_ID \
        --cache-from $CI_REGISTRY_SPEED_IMAGE:$SANITIZED_REF \
        -t $CI_REGISTRY_SPEED_IMAGE:$CI_PIPELINE_ID \
        -t $CI_REGISTRY_SPEED_IMAGE:$SANITIZED_REF
    - docker push $CI_REGISTRY_SPEED_IMAGE:$CI_PIPELINE_ID
    - docker push $CI_REGISTRY_SPEED_IMAGE:$SANITIZED_REF
  extends: .speed-common

# Run linting
speed-lint:
  stage: test
  image: $CI_REGISTRY_SPEED_IMAGE/test:$CI_PIPELINE_ID
  script:
    - cd /app
    - pnpm --filter speed run lint
  extends: .speed-common

# Run tests
speed-unit-test:
  stage: test
  image: mcr.microsoft.com/playwright:v1.54.0-noble
  before_script:
    - npm install --global corepack@latest
    - corepack enable
    - corepack prepare pnpm@latest-10 --activate
    - pnpm config set store-dir .pnpm-store
    # If no .npmrc is included in the repo, generate a temporary one that is configured to publish to GitLab's NPM registry
    - |
      if [[ ! -f .npmrc ]]; then
        echo 'No .npmrc found! Creating one now. Please review the following link for more information: https://docs.gitlab.com/ee/user/packages/npm_registry/#with-the-npmrc-file'
        {
          echo "@${CI_PROJECT_ROOT_NAMESPACE}:registry=${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/npm/"
          echo "${CI_API_V4_URL#http*:}/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=\${CI_JOB_TOKEN}"
        } >> .npmrc
      fi
    - pnpm install --frozen-lockfile
  script:
    - pnpm --filter speed run test
  extends: .speed-common
