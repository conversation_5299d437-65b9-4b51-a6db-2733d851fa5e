import { defineConfig } from 'vitest/config';

// This is our root config that defines the workspace
import { storybookTest } from '@storybook/addon-vitest/vitest-plugin';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
const dirname =
  typeof __dirname !== 'undefined'
    ? __dirname
    : path.dirname(fileURLToPath(import.meta.url));

// More info at: https://storybook.js.org/docs/next/writing-tests/integrations/vitest-addon
export default defineConfig({
  test: {
    // We don't need glob\al settings here since they're in vitest.shared.ts
    // and imported by each project
    projects: [
      {
        extends: true,
        plugins: [
          // The plugin will run tests for the stories defined in your Storybook config
          // See options at: https://storybook.js.org/docs/next/writing-tests/integrations/vitest-addon#storybooktest
          storybookTest({
            configDir: path.join(dirname, '.storybook'),
          }),
        ],
        test: {
          alias: {
            '~': path.resolve(__dirname, './src'),
          },
          name: 'storybook',
          isolate: true,
          globals: true,
          environment: 'jsdom',
          browser: {
            enabled: true,
            headless: true,
            provider: 'playwright',
            instances: [
              {
                browser: 'chromium',
              },
            ],
          },
          setupFiles: ['.storybook/vitest.setup.ts'],
        },
      },
      {
        // add "extends: true" to inherit the options from the root config
        extends: true,
        test: {
          include: ['./src/**/*.(test|spec).{ts,js,tsx,jsx}'],
          // it is recommended to define a name when using inline configs
          name: 'speed',
          globals: true,
          environment: 'jsdom',
          alias: {
            '~': path.resolve(__dirname, './src'),
          },
          setupFiles: ['setup-test.ts'],
        },
      },
    ],
  },
});
