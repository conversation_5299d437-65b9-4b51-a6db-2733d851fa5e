{"name": "vitalize", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"test": "pnpm -r run test", "lint": "eslint", "dev:speed": "pnpm --filter speed run dev", "prepare": "husky"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.13.1", "devDependencies": {"@eslint/js": "^9.26.0", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-storybook": "^9.0.17", "globals": "^16.1.0", "husky": "^9.1.7", "lint-staged": "^15.5.2", "prettier": "3.0.1", "typescript-eslint": "^8.32.0", "vitest": "^3.1.3"}}