version: '3.8'

services:
  cardcastle:
    build:
      context: .
      dockerfile: ./apps/cardcastle/Dockerfile
    ports:
      - '8080:80'
    restart: unless-stopped
    environment:
      - NODE_ENV=production
    networks:
      - vitalize-network

  speed:
    build:
      context: .
      dockerfile: ./apps/speed/Dockerfile
    ports:
      - '3000:3000'
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=3000
    networks:
      - vitalize-network

networks:
  vitalize-network:
    driver: bridge
